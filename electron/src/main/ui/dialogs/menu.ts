import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import { DIALOG_MARGIN_TOP, DIALOG_MARGIN } from '@electron/renderer/constants/design';

export const showMenuDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
) => {
  console.log('[MenuDialog] Showing menu dialog at position:', { x, y });

  const menuWidth = 330;
  let isInitialShow = true;

  const dialog = Application.instance.dialogs.show({
    name: 'menu',
    browserWindow,
    getBounds: () => ({
      width: menuWidth,
      height: 510,
      x: x - menuWidth + DIALOG_MARGIN,
      y: y - DIALOG_MARGIN_TOP,
    }),
    onWindowBoundsUpdate: (disposition) => {
      console.log('[MenuDialog] Window bounds updated:', disposition, 'isInitialShow:', isInitialShow);

      // 避免在初始显示时立即隐藏
      if (isInitialShow) {
        console.log('[MenuDialog] Ignoring initial bounds update');
        isInitialShow = false;
        return;
      }

      // 只在窗口移动或调整大小时隐藏菜单
      if (disposition === 'move' || disposition === 'resize') {
        console.log('[MenuDialog] Hiding dialog due to window', disposition);
        dialog.hide();
      }
    },
  });

  // 延迟重置初始显示标志，确保初始设置完成
  setTimeout(() => {
    isInitialShow = false;
  }, 100);

  console.log('[MenuDialog] Dialog creation result:', !!dialog);
};
