import { ipcMain } from 'electron';
import { eq, and, or, like, desc, asc, sql } from 'drizzle-orm';
import { LibSQLAdapter } from './database/adapters/libsql-adapter';
import { CacheManager } from './database/cache-manager';
import * as schemas from './database/schemas';
import { DEFAULT_SETTINGS } from '../../renderer/constants/settings';
import {
  IFindOperation,
  IInsertOperation,
  IRemoveOperation,
  IUpdateOperation,
  IBookmark,
  IHistoryItem,
  IFavicon,
  ISettings,
} from '@electron/types';
import { Application } from '../core/application';

export class LibSQLStorageService {
  private adapter: LibSQLAdapter;
  private db: ReturnType<LibSQLAdapter['getDatabase']>;
  private cache: CacheManager;
  private isInitialized: boolean = false;

  // 兼容性属性 - 与NeDB存储服务保持一致
  // 现在这些属性委托给CacheManager，但保持可写以兼容现有代码
  private _history: IHistoryItem[] = [];
  private _favicons: Map<string, string> = new Map();

  public get history(): IHistoryItem[] {
    return this._history; // 暂时使用内部数组，历史记录缓存待实现
  }

  public set history(value: IHistoryItem[]) {
    this._history = value;
  }

  public get bookmarks(): IBookmark[] {
    return this.cache.getBookmarksCache();
  }

  public get favicons(): Map<string, string> {
    return this._favicons; // 暂时使用内部Map，图标缓存待完善
  }

  public set favicons(value: Map<string, string>) {
    this._favicons = value;
  }

  constructor() {
    this.adapter = new LibSQLAdapter();
    this.cache = new CacheManager();
  }

  public addHistory(item: IHistoryItem) {
    this.history.push(item);
  }

  async initialize(): Promise<void> {
    await this.adapter.initialize();
    this.db = this.adapter.getDatabase();
    this.setupIPC();

    // 加载数据到内存中以保持兼容性
    await this.loadBookmarks();
    await this.loadFavicons();
    await this.loadHistory();

    // 初始化缓存管理器
    await this.initializeCache();

    // 初始化配置管理
    await this.initializeSettings();

    // 标记初始化完成
    this.isInitialized = true;

    console.log('LibSQLStorageService 初始化完成');


  }

  // 初始化缓存管理器
  private async initializeCache(): Promise<void> {
    try {
      console.log('[LibSQL Storage] 初始化缓存管理器...');

      // 设置图标缓存（书签缓存已在loadBookmarks中设置）
      this.cache.setFaviconsCache(this.favicons);

      console.log('[LibSQL Storage] 缓存管理器初始化完成');
      console.log('[LibSQL Storage] 缓存统计:', this.cache.getCacheStats());
    } catch (error) {
      console.error('[LibSQL Storage] 缓存初始化失败:', error);
      // 缓存初始化失败不应该影响主要功能
    }
  }

  // 缓存管理公共方法
  public getCacheStats() {
    return this.cache.getCacheStats();
  }

  public clearCache() {
    this.cache.clearCache();
  }

  public async refreshCache() {
    try {
      await this.cache.refreshCacheIfNeeded(
        async () => {
          // 直接从数据库查询，避免缓存循环
          const whereCondition = this.buildWhereCondition(schemas.bookmarks, {});
          const results = await this.db
            .select()
            .from(schemas.bookmarks)
            .where(whereCondition)
            .orderBy(asc(schemas.bookmarks.orderIndex));
          return results.map(this.convertBookmarkToNeDBFormat);
        },
        async () => {
          return this.favicons;
        }
      );
    } catch (error) {
      console.error('[LibSQL Storage] 缓存刷新失败:', error);
    }
  }

  // 判断是否为简单查询（适合缓存）
  private isSimpleQuery(query: any): boolean {
    // 只有基本字段查询才使用缓存
    const allowedFields = ['_id', 'title', 'url', 'isFolder', 'parent', 'static'];
    const queryFields = Object.keys(query);

    // 空查询或只包含允许字段的查询
    return queryFields.length === 0 || queryFields.every(field => allowedFields.includes(field));
  }

  private async loadBookmarks(): Promise<void> {
    console.log('[LibSQL Storage] Loading bookmarks...');

    // 直接查询数据库，避免缓存循环
    const whereCondition = this.buildWhereCondition(schemas.bookmarks, {});
    const results = await this.db
      .select()
      .from(schemas.bookmarks)
      .where(whereCondition)
      .orderBy(asc(schemas.bookmarks.orderIndex));

    const items = results.map(this.convertBookmarkToNeDBFormat);
    console.log(`[LibSQL Storage] Found ${items.length} bookmarks in database`);

    // 先检查默认文件夹是否存在（基于数据库数据）
    let barFolder = items.find((x) => x.static === 'main');
    let otherFolder = items.find((x) => x.static === 'other');
    let mobileFolder = items.find((x) => x.static === 'mobile');

    if (!barFolder) {
      console.log('[LibSQL Storage] 创建默认书签栏文件夹...');
      barFolder = await this.insert<IBookmark>({
        scope: 'bookmarks',
        item: {
          title: '书签栏',
          url: '',
          isFolder: true,
          static: 'main',
          order: 0,
        },
      });
      console.log('[LibSQL Storage] 书签栏文件夹创建完成:', barFolder._id);
    }

    if (!otherFolder) {
      console.log('[LibSQL Storage] 创建其他书签文件夹...');
      otherFolder = await this.insert<IBookmark>({
        scope: 'bookmarks',
        item: {
          title: '其他书签',
          url: '',
          isFolder: true,
          static: 'other',
          order: 1,
        },
      });
      console.log('[LibSQL Storage] 其他书签文件夹创建完成:', otherFolder._id);
    }

    if (!mobileFolder) {
      console.log('[LibSQL Storage] 创建移动设备书签文件夹...');
      mobileFolder = await this.insert<IBookmark>({
        scope: 'bookmarks',
        item: {
          title: '移动设备书签',
          url: '',
          isFolder: true,
          static: 'mobile',
          order: 2,
        },
      });
      console.log('[LibSQL Storage] 移动设备书签文件夹创建完成:', mobileFolder._id);
    }

    // 重新查询数据库以获取包含新创建文件夹的完整数据
    console.log('[LibSQL Storage] 重新从数据库加载所有书签...');
    const finalWhereCondition = this.buildWhereCondition(schemas.bookmarks, {});
    const finalResults = await this.db
      .select()
      .from(schemas.bookmarks)
      .where(finalWhereCondition)
      .orderBy(asc(schemas.bookmarks.orderIndex));

    const allItems = finalResults.map(this.convertBookmarkToNeDBFormat);
    const sortedItems = allItems.sort((a, b) => (a.order || 0) - (b.order || 0));

    this.cache.clearCache();
    this.cache.setBookmarksCache(sortedItems);

    console.log(`[LibSQL Storage] 书签加载完成，共 ${sortedItems.length} 个书签`);
  }

  private async loadFavicons(): Promise<void> {
    console.log('[LibSQL Storage] Loading favicons...');
    const favicons = await this.find<IFavicon>({ scope: 'favicons', query: {} });

    this.favicons.clear();
    favicons.forEach((favicon) => {
      if (favicon.url && favicon.data) {
        this.favicons.set(favicon.url, favicon.data);
      }
    });

    console.log(`[LibSQL Storage] Loaded ${this.favicons.size} favicons`);
  }

  private async loadHistory(): Promise<void> {
    console.log('[LibSQL Storage] Loading history...');
    const items = await this.find<IHistoryItem>({ scope: 'history', query: {} });
    this.history = items;
    console.log(`[LibSQL Storage] Loaded ${this.history.length} history items`);
  }

  private setupIPC(): void {
    // 兼容现有的IPC接口
    ipcMain.handle('storage-get', async (e, data: IFindOperation) => {
      return await this.find(data);
    });

    ipcMain.handle('storage-get-one', async (e, data: IFindOperation) => {
      return await this.findOne(data);
    });

    ipcMain.handle('storage-find', async (e, data: IFindOperation) => {
      return await this.find(data);
    });

    ipcMain.handle('storage-insert', async (e, data: IInsertOperation) => {
      return await this.insert(data);
    });

    ipcMain.handle('storage-remove', async (e, data: IRemoveOperation) => {
      return await this.remove(data);
    });

    ipcMain.handle('storage-update', async (e, data: IUpdateOperation) => {
      return await this.update(data);
    });

    // 书签相关的特殊接口
    ipcMain.handle('bookmarks-get', async (e) => {
      console.log('[LibSQL Storage] Getting bookmarks...');
      console.log(`[LibSQL Storage] Returning ${this.bookmarks.length} bookmarks from memory`);
      return this.bookmarks;
    });

    // 导入导出功能 - 暂时保持简单实现
    ipcMain.handle('import-bookmarks', async () => {
      // TODO: 实现书签导入功能
      console.log('[LibSQL Storage] Import bookmarks - not implemented yet');
      return [];
    });

    ipcMain.handle('export-bookmarks', async () => {
      // TODO: 实现书签导出功能
      console.log('[LibSQL Storage] Export bookmarks - not implemented yet');
    });

    // 添加专门的IPC处理器
    ipcMain.handle('bookmarks-get-folders', async (e) => {
      console.log('[LibSQL Storage] Getting bookmark folders');
      try {
        const folders = await this.findBookmarks({ isFolder: true });
        // 确保返回的数据是可序列化的
        const serializableFolders = folders.map(folder => ({
          _id: folder._id,
          title: folder.title,
          parent: folder.parent,
          children: folder.children || [],
          isFolder: folder.isFolder,
          static: folder.static,
          order: folder.order
        }));
        console.log('[LibSQL Storage] Returning serializable folders:', serializableFolders.length);
        return serializableFolders;
      } catch (error) {
        console.error('[LibSQL Storage] Error getting bookmark folders:', error);
        return [];
      }
    });

    ipcMain.handle('history-get', async (e) => {
      console.log('[LibSQL Storage] Getting history');
      try {
        const history = await this.findHistory({});
        console.log('[LibSQL Storage] Returning history items:', history.length);
        return history;
      } catch (error) {
        console.error('[LibSQL Storage] Error getting history:', error);
        return [];
      }
    });

    ipcMain.handle('topsites-get', async (e, count = 10) => {
      console.log('[LibSQL Storage] Getting top sites, count:', count);
      try {
        // 获取最近的历史记录，按访问时间排序
        const recentHistory = await this.findHistory({});

        // 过滤出有标题的记录，并获取favicon
        const list = [];
        let c = 0;

        for (let i = recentHistory.length - 1; i >= 0 && c < count; i--) {
          const item = recentHistory[i];
          if (item.title && item.title.trim() !== '') {
            // 查找对应的favicon
            const favicon = this.favicons.get(item.favicon) || '';
            list.push({
              ...item,
              favicon: favicon
            });
            c++;
          }
        }

        console.log('[LibSQL Storage] Returning top sites:', list.length);
        return list;
      } catch (error) {
        console.error('[LibSQL Storage] Error getting top sites:', error);
        return [];
      }
    });

    ipcMain.on('history-remove', async (e, ids: string[]) => {
      console.log('[LibSQL Storage] Removing history items:', ids.length);
      try {
        for (const id of ids) {
          await this.removeHistory({ _id: id }, false);
        }
        console.log('[LibSQL Storage] History items removed successfully');
      } catch (error) {
        console.error('[LibSQL Storage] Error removing history items:', error);
      }
    });

    // 添加书签相关的专门处理器
    ipcMain.handle('bookmarks-add', async (e, bookmark) => {
      console.log('[LibSQL Storage] Adding bookmark:', bookmark);
      try {
        const result = await this.insertBookmark(bookmark);
        console.log('[LibSQL Storage] Bookmark added successfully:', result._id);

        // 广播书签更新事件
        Application.instance.windows.broadcast('reload-bookmarks');

        // 更新所有窗口的书签状态
        Application.instance.windows.list.forEach((x) => {
          x.viewManager.selected?.updateBookmark();
        });

        return result;
      } catch (error) {
        console.error('[LibSQL Storage] Error adding bookmark:', error);
        throw error;
      }
    });

    ipcMain.on('bookmarks-update', async (e, id, change) => {
      console.log('[LibSQL Storage] Updating bookmark:', id, change);
      try {
        await this.updateBookmarks({ _id: id }, change, false);
        console.log('[LibSQL Storage] Bookmark updated successfully');

        // 广播书签更新事件
        Application.instance.windows.broadcast('reload-bookmarks');

        // 更新所有窗口的书签状态
        Application.instance.windows.list.forEach((x) => {
          x.viewManager.selected?.updateBookmark();
        });
      } catch (error) {
        console.error('[LibSQL Storage] Error updating bookmark:', error);
      }
    });

    ipcMain.on('bookmarks-remove', async (e, ids: string[]) => {
      console.log('[LibSQL Storage] Removing bookmarks:', ids);
      try {
        for (const id of ids) {
          await this.removeBookmarks({ _id: id }, false);
        }
        console.log('[LibSQL Storage] Bookmarks removed successfully');

        // 广播书签更新事件
        Application.instance.windows.broadcast('reload-bookmarks');

        // 更新所有窗口的书签状态
        Application.instance.windows.list.forEach((x) => {
          x.viewManager.selected?.updateBookmark();
        });
      } catch (error) {
        console.error('[LibSQL Storage] Error removing bookmarks:', error);
      }
    });

    // 配置管理相关的IPC处理器
    ipcMain.on('get-settings-sync', async (event) => {
      try {
        const settings = await this.getSettings();
        event.returnValue = settings;
      } catch (error) {
        console.error('[LibSQL Storage] 获取设置失败:', error);
        event.returnValue = DEFAULT_SETTINGS;
      }
    });

    ipcMain.handle('get-settings', async (event) => {
      try {
        const settings = await this.getSettings();

        // 主动发送update-settings事件到请求的窗口
        const webContents = event.sender;
        webContents.send('update-settings', settings);

        return settings;
      } catch (error) {
        console.error('[LibSQL Storage] 获取设置失败:', error);
        const defaultSettings = DEFAULT_SETTINGS;

        // 即使失败也要发送默认设置
        const webContents = event.sender;
        webContents.send('update-settings', defaultSettings);

        return defaultSettings;
      }
    });

    ipcMain.handle('save-settings', async (event, settings: ISettings) => {
      try {
        await this.updateSettingsNew(settings);
        // 广播设置更新
        await this.broadcastSettingsUpdate();
        return { success: true };
      } catch (error) {
        console.error('[LibSQL Storage] 保存设置失败:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('get-config', async (event, key: string) => {
      try {
        return await this.getConfig(key);
      } catch (error) {
        console.error('[LibSQL Storage] 获取配置项失败:', error);
        return null;
      }
    });

    ipcMain.handle('set-config', async (event, key: string, value: any) => {
      try {
        await this.setConfig(key, value);
        return { success: true };
      } catch (error) {
        console.error('[LibSQL Storage] 设置配置项失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 配置管理实用工具
    ipcMain.handle('reset-settings', async () => {
      try {
        await this.resetSettings();
        return { success: true };
      } catch (error) {
        console.error('[LibSQL Storage] 重置设置失败:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('export-settings', async () => {
      try {
        const settings = await this.getSettings();
        return {
          success: true,
          data: {
            version: '1.0.0',
            exportedAt: new Date().toISOString(),
            settings
          }
        };
      } catch (error) {
        console.error('[LibSQL Storage] 导出设置失败:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('import-settings', async (event, settingsData: any) => {
      try {
        if (settingsData && settingsData.settings) {
          await this.updateSettingsNew(settingsData.settings);
          return { success: true };
        } else {
          return { success: false, error: '无效的设置数据格式' };
        }
      } catch (error) {
        console.error('[LibSQL Storage] 导入设置失败:', error);
        return { success: false, error: error.message };
      }
    });

    console.log('[LibSQL Storage] All IPC handlers registered');
  }

  // 兼容现有的find接口
  async find<T>(data: IFindOperation): Promise<T[]> {
    const { scope, query } = data;

    try {
      switch (scope) {
        case 'bookmarks':
          return await this.findBookmarks(query) as T[];
        case 'history':
          return await this.findHistory(query) as T[];
        case 'favicons':
          return await this.findFavicons(query) as T[];
        case 'formfill':
          return await this.findFormFill(query) as T[];
        case 'startupTabs':
          return await this.findStartupTabs(query) as T[];
        case 'permissions':
          return await this.findPermissions(query) as T[];
        case 'settings':
          return await this.findSettings(query) as T[];
        default:
          throw new Error(`未知的scope: ${scope}`);
      }
    } catch (error) {
      console.error(`LibSQL查询失败 [${scope}]:`, error);
      throw error;
    }
  }

  // 兼容现有的findOne接口
  async findOne<T>(data: IFindOperation): Promise<T | null> {
    const results = await this.find<T>(data);
    return results.length > 0 ? results[0] : null;
  }

  // 兼容现有的insert接口
  async insert<T>(data: IInsertOperation): Promise<T> {
    const { scope, item } = data;

    try {
      switch (scope) {
        case 'bookmarks':
          return await this.insertBookmark(item) as T;
        case 'history':
          return await this.insertHistory(item) as T;
        case 'favicons':
          return await this.insertFavicon(item) as T;
        case 'formfill':
          return await this.insertFormFill(item) as T;
        case 'startupTabs':
          return await this.insertStartupTab(item) as T;
        case 'permissions':
          return await this.insertPermission(item) as T;
        case 'settings':
          return await this.insertSetting(item) as T;
        default:
          throw new Error(`未知的scope: ${scope}`);
      }
    } catch (error) {
      console.error(`LibSQL插入失败 [${scope}]:`, error);
      throw error;
    }
  }

  // 兼容现有的remove接口
  async remove(data: IRemoveOperation): Promise<number> {
    const { scope, query, multi = false } = data;

    try {
      switch (scope) {
        case 'bookmarks':
          return await this.removeBookmarks(query, multi);
        case 'history':
          return await this.removeHistory(query, multi);
        case 'favicons':
          return await this.removeFavicons(query, multi);
        case 'formfill':
          return await this.removeFormFill(query, multi);
        case 'startupTabs':
          return await this.removeStartupTabs(query, multi);
        case 'permissions':
          return await this.removePermissions(query, multi);
        case 'settings':
          return await this.removeSettings(query, multi);
        default:
          throw new Error(`未知的scope: ${scope}`);
      }
    } catch (error) {
      console.error(`LibSQL删除失败 [${scope}]:`, error);
      throw error;
    }
  }

  // 兼容现有的update接口
  async update(data: IUpdateOperation): Promise<number> {
    const { scope, query, value, multi = false } = data;

    try {
      switch (scope) {
        case 'bookmarks':
          return await this.updateBookmarks(query, value, multi);
        case 'history':
          return await this.updateHistory(query, value, multi);
        case 'favicons':
          return await this.updateFavicons(query, value, multi);
        case 'formfill':
          return await this.updateFormFill(query, value, multi);
        case 'startupTabs':
          return await this.updateStartupTabs(query, value, multi);
        case 'permissions':
          return await this.updatePermissions(query, value, multi);
        case 'settings':
          return await this.updateSettings(query, value, multi);
        default:
          throw new Error(`未知的scope: ${scope}`);
      }
    } catch (error) {
      console.error(`LibSQL更新失败 [${scope}]:`, error);
      throw error;
    }
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 将NeDB风格的查询转换为Drizzle查询条件
  // 字段名映射：NeDB -> LibSQL
  private mapFieldName(tableName: string, nedbFieldName: string): string | null {
    const fieldMappings: Record<string, Record<string, string>> = {
      bookmarks: {
        '_id': 'id',
        'parent': 'parentId',
        'order': 'orderIndex',
        'static': 'staticType'
      },
      history: {
        '_id': 'id',
        'date': 'visitTime'
      },
      favicons: {
        '_id': 'id'
      },
      formFillData: {
        '_id': 'id'
      },
      startupTabs: {
        '_id': 'id',
        'pinned': 'pinned',
        'windowId': 'windowId'
      },
      permissions: {
        '_id': 'id',
        'url': 'origin',
        'permission': 'permissionType',
        'mediaTypes': null // 忽略mediaTypes字段，permissions表中不存在
      },
      settings: {
        '_id': 'id'
      }
    };

    const mapping = fieldMappings[tableName];
    return mapping && mapping[nedbFieldName] ? mapping[nedbFieldName] : nedbFieldName;
  }

  private buildWhereCondition(table: any, query: any) {
    if (!query || Object.keys(query).length === 0) {
      return undefined;
    }

    const conditions = [];

    // 根据表对象确定表名
    let tableName = '';
    if (table === schemas.bookmarks) tableName = 'bookmarks';
    else if (table === schemas.history) tableName = 'history';
    else if (table === schemas.favicons) tableName = 'favicons';
    else if (table === schemas.formFillData) tableName = 'formFillData';
    else if (table === schemas.startupTabs) tableName = 'startupTabs';
    else if (table === schemas.permissions) tableName = 'permissions';
    else if (table === schemas.settings) tableName = 'settings';

    for (const [key, value] of Object.entries(query)) {
      if (value === null || value === undefined) {
        continue;
      }

      // 映射字段名
      const mappedKey = this.mapFieldName(tableName, key);

      // 如果映射为null，表示忽略此字段
      if (mappedKey === null) {
        continue;
      }

      const column = table[mappedKey];

      if (!column) {
        console.warn(`字段 ${key} (映射为 ${mappedKey}) 在表 ${tableName} 中不存在`);
        continue;
      }

      if (typeof value === 'object' && value !== null) {
        // 处理MongoDB风格的操作符
        if ('$regex' in value) {
          conditions.push(like(column, `%${value.$regex}%`));
        } else if ('$in' in value && Array.isArray(value.$in)) {
          // 暂时用OR条件模拟$in操作
          const inConditions = value.$in.map((v: any) => eq(column, v));
          conditions.push(or(...inConditions));
        } else if ('$ne' in value) {
          conditions.push(sql`${column} != ${value.$ne}`);
        }
      } else {
        conditions.push(eq(column, value));
      }
    }

    return conditions.length > 0 ? and(...conditions) : undefined;
  }

  // 书签相关操作
  private async findBookmarks(query: any): Promise<IBookmark[]> {
    // 尝试从缓存中查找（仅对简单查询）
    if (this.cache.isCacheEnabled() && this.isSimpleQuery(query)) {
      const cachedResults = this.cache.findBookmarksInCache(query);
      if (cachedResults.length > 0 || Object.keys(query).length === 0) {
        console.log(`[LibSQL Storage] 从缓存返回 ${cachedResults.length} 个书签`);
        return cachedResults;
      }
    }

    const whereCondition = this.buildWhereCondition(schemas.bookmarks, query);

    const results = await this.db
      .select()
      .from(schemas.bookmarks)
      .where(whereCondition)
      .orderBy(asc(schemas.bookmarks.orderIndex));

    // 转换为NeDB兼容格式
    return results.map(this.convertBookmarkToNeDBFormat);
  }

  private async insertBookmark(item: any): Promise<IBookmark> {
    const now = new Date();
    const bookmark = {
      id: item._id || this.generateId(),
      title: item.title || '',
      url: item.url || '',
      favicon: item.favicon || '',
      isFolder: item.isFolder || false,
      parentId: item.parent || null,
      orderIndex: item.order || 0,
      staticType: item.static || null,
      expanded: item.expanded || false,
      aiTags: null,
      aiSummary: null,
      aiCategory: null,
      createdAt: now,
      updatedAt: now
    };

    const result = await this.db
      .insert(schemas.bookmarks)
      .values(bookmark)
      .returning();

    const newBookmark = this.convertBookmarkToNeDBFormat(result[0]);

    // 更新缓存（只在非初始化阶段）
    if (this.cache.isCacheEnabled() && this.isInitialized) {
      this.cache.addBookmarkToCache(newBookmark);
    }

    return newBookmark;
  }

  private async removeBookmarks(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.bookmarks, query);

    if (!whereCondition) {
      throw new Error('删除操作必须提供有效的查询条件');
    }

    // 先获取要删除的书签，以便从内存中移除
    const toDelete = await this.findBookmarks(query);

    const result = await this.db
      .delete(schemas.bookmarks)
      .where(whereCondition);

    // 从缓存中移除删除的书签（只在初始化完成后）
    if (this.cache.isCacheEnabled() && this.isInitialized) {
      for (const bookmark of toDelete) {
        this.cache.removeBookmarkFromCache(bookmark._id);
      }
    }

    return (result as any).changes || 0;
  }

  private async updateBookmarks(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.bookmarks, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供有效的查询条件');
    }

    // 先获取要更新的书签
    const toUpdate = await this.findBookmarks(query);

    const updateData: any = {
      updatedAt: new Date()
    };

    // 映射字段名
    if (value.title !== undefined) updateData.title = value.title;
    if (value.url !== undefined) updateData.url = value.url;
    if (value.favicon !== undefined) updateData.favicon = value.favicon;
    if (value.isFolder !== undefined) updateData.isFolder = value.isFolder;
    if (value.parent !== undefined) updateData.parentId = value.parent;
    if (value.order !== undefined) updateData.orderIndex = value.order;
    if (value.static !== undefined) updateData.staticType = value.static;
    if (value.expanded !== undefined) updateData.expanded = value.expanded;

    const result = await this.db
      .update(schemas.bookmarks)
      .set(updateData)
      .where(whereCondition);

    // 更新缓存（只在初始化完成后）
    if (this.cache.isCacheEnabled() && this.isInitialized) {
      for (const bookmark of toUpdate) {
        this.cache.updateBookmarkInCache(bookmark._id, value);
      }
    }

    return (result as any).changes || 0;
  }

  // 转换LibSQL格式到NeDB格式
  private convertBookmarkToNeDBFormat(bookmark: any): IBookmark {
    return {
      _id: bookmark.id,
      title: bookmark.title,
      url: bookmark.url,
      favicon: bookmark.favicon,
      isFolder: bookmark.isFolder,
      parent: bookmark.parentId,
      order: bookmark.orderIndex,
      expanded: bookmark.expanded,
      static: bookmark.staticType as any,
      children: [] // 这个字段需要单独查询子项目
    };
  }

  // 历史记录相关操作
  private async findHistory(query: any): Promise<IHistoryItem[]> {
    const whereCondition = this.buildWhereCondition(schemas.history, query);

    const results = await this.db
      .select()
      .from(schemas.history)
      .where(whereCondition)
      .orderBy(desc(schemas.history.visitTime));

    return results.map(this.convertHistoryToNeDBFormat);
  }

  private async insertHistory(item: any): Promise<IHistoryItem> {
    const now = new Date();
    const historyItem = {
      id: item._id || this.generateId(),
      url: item.url,
      title: item.title || '',
      visitTime: item.date ? new Date(item.date) : now,
      favicon: item.favicon || '',
      visitCount: 1,
      aiInsights: null,
      aiRelevanceScore: null,
      createdAt: now
    };

    const result = await this.db
      .insert(schemas.history)
      .values(historyItem)
      .returning();

    return this.convertHistoryToNeDBFormat(result[0]);
  }

  private async removeHistory(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.history, query);

    if (!whereCondition) {
      throw new Error('删除操作必须提供有效的查询条件');
    }

    const result = await this.db
      .delete(schemas.history)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  private async updateHistory(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.history, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供有效的查询条件');
    }

    const updateData: any = {};
    if (value.title !== undefined) updateData.title = value.title;
    if (value.url !== undefined) updateData.url = value.url;
    if (value.favicon !== undefined) updateData.favicon = value.favicon;
    if (value.date !== undefined) updateData.visitTime = new Date(value.date);

    const result = await this.db
      .update(schemas.history)
      .set(updateData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  private convertHistoryToNeDBFormat(history: any): IHistoryItem {
    return {
      _id: history.id,
      url: history.url,
      title: history.title,
      date: history.visitTime.getTime(),
      favicon: history.favicon
    };
  }

  // 网站图标相关操作
  private async findFavicons(query: any): Promise<IFavicon[]> {
    const whereCondition = this.buildWhereCondition(schemas.favicons, query);

    const results = await this.db
      .select()
      .from(schemas.favicons)
      .where(whereCondition);

    return results.map(this.convertFaviconToNeDBFormat);
  }

  private async insertFavicon(item: any): Promise<IFavicon> {
    const favicon = {
      url: item.url,
      data: item.data,
      mimeType: item.mimeType || 'image/png',
      createdAt: new Date()
    };

    const result = await this.db
      .insert(schemas.favicons)
      .values(favicon)
      .returning();

    return this.convertFaviconToNeDBFormat(result[0]);
  }

  private async removeFavicons(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.favicons, query);

    if (!whereCondition) {
      throw new Error('删除操作必须提供有效的查询条件');
    }

    const result = await this.db
      .delete(schemas.favicons)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  private async updateFavicons(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.favicons, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供有效的查询条件');
    }

    const updateData: any = {};
    if (value.data !== undefined) updateData.data = value.data;
    if (value.mimeType !== undefined) updateData.mimeType = value.mimeType;

    const result = await this.db
      .update(schemas.favicons)
      .set(updateData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  private convertFaviconToNeDBFormat(favicon: any): IFavicon {
    return {
      url: favicon.url,
      data: favicon.data
    };
  }

  // 表单填充相关操作
  private async findFormFill(query: any): Promise<any[]> {
    const whereCondition = this.buildWhereCondition(schemas.formFillData, query);

    const results = await this.db
      .select()
      .from(schemas.formFillData)
      .where(whereCondition);

    return results.map(item => ({
      _id: item.id,
      type: item.type,
      url: item.url,
      favicon: item.favicon,
      fields: JSON.parse(item.fields)
    }));
  }

  private async insertFormFill(item: any): Promise<any> {
    const now = new Date();
    const formFillData = {
      id: item._id || this.generateId(),
      type: item.type,
      url: item.url,
      favicon: item.favicon || '',
      fields: JSON.stringify(item.fields),
      createdAt: now,
      updatedAt: now
    };

    const result = await this.db
      .insert(schemas.formFillData)
      .values(formFillData)
      .returning();

    return {
      _id: result[0].id,
      type: result[0].type,
      url: result[0].url,
      favicon: result[0].favicon,
      fields: JSON.parse(result[0].fields)
    };
  }

  private async removeFormFill(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.formFillData, query);

    if (!whereCondition) {
      throw new Error('删除操作必须提供有效的查询条件');
    }

    const result = await this.db
      .delete(schemas.formFillData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  private async updateFormFill(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.formFillData, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供查询条件');
    }

    const updateData: any = { updatedAt: new Date() };
    if (value.type !== undefined) updateData.type = value.type;
    if (value.url !== undefined) updateData.url = value.url;
    if (value.favicon !== undefined) updateData.favicon = value.favicon;
    if (value.fields !== undefined) updateData.fields = JSON.stringify(value.fields);

    const result = await this.db
      .update(schemas.formFillData)
      .set(updateData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  // 启动标签页相关操作
  private async findStartupTabs(query: any): Promise<any[]> {
    const whereCondition = this.buildWhereCondition(schemas.startupTabs, query);

    const results = await this.db
      .select()
      .from(schemas.startupTabs)
      .where(whereCondition)
      .orderBy(asc(schemas.startupTabs.orderIndex));

    return results.map(item => ({
      _id: item.id,
      title: item.title,
      url: item.url,
      favicon: item.favicon,
      isUserDefined: item.isUserDefined
    }));
  }

  private async insertStartupTab(item: any): Promise<any> {
    const startupTab = {
      id: item._id || this.generateId(),
      title: item.title || '',
      url: item.url,
      favicon: item.favicon || '',
      isUserDefined: item.isUserDefined || false,
      orderIndex: item.order || 0,
      createdAt: new Date()
    };

    const result = await this.db
      .insert(schemas.startupTabs)
      .values(startupTab)
      .returning();

    return {
      _id: result[0].id,
      title: result[0].title,
      url: result[0].url,
      favicon: result[0].favicon,
      isUserDefined: result[0].isUserDefined
    };
  }

  private async removeStartupTabs(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.startupTabs, query);

    // 如果没有查询条件，表示删除所有记录
    const result = whereCondition
      ? await this.db.delete(schemas.startupTabs).where(whereCondition)
      : await this.db.delete(schemas.startupTabs);

    return (result as any).changes || 0;
  }

  private async updateStartupTabs(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.startupTabs, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供查询条件');
    }

    const updateData: any = {};
    if (value.title !== undefined) updateData.title = value.title;
    if (value.url !== undefined) updateData.url = value.url;
    if (value.favicon !== undefined) updateData.favicon = value.favicon;
    if (value.isUserDefined !== undefined) updateData.isUserDefined = value.isUserDefined;

    const result = await this.db
      .update(schemas.startupTabs)
      .set(updateData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  // 权限相关操作
  private async findPermissions(query: any): Promise<any[]> {
    const whereCondition = this.buildWhereCondition(schemas.permissions, query);

    const results = await this.db
      .select()
      .from(schemas.permissions)
      .where(whereCondition);

    return results.map(item => ({
      _id: item.id,
      origin: item.origin,
      permission: item.permissionType,
      granted: item.granted
    }));
  }

  private async insertPermission(item: any): Promise<any> {
    const permission = {
      id: item._id || this.generateId(),
      origin: item.origin,
      permissionType: item.permission,
      granted: item.granted || false,
      createdAt: new Date()
    };

    const result = await this.db
      .insert(schemas.permissions)
      .values(permission)
      .returning();

    return {
      _id: result[0].id,
      origin: result[0].origin,
      permission: result[0].permissionType,
      granted: result[0].granted
    };
  }

  private async removePermissions(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.permissions, query);

    if (!whereCondition) {
      throw new Error('删除操作必须提供有效的查询条件');
    }

    const result = await this.db
      .delete(schemas.permissions)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  private async updatePermissions(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.permissions, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供查询条件');
    }

    const updateData: any = {};
    if (value.origin !== undefined) updateData.origin = value.origin;
    if (value.permission !== undefined) updateData.permissionType = value.permission;
    if (value.granted !== undefined) updateData.granted = value.granted;

    const result = await this.db
      .update(schemas.permissions)
      .set(updateData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  // 设置相关操作
  private async findSettings(query: any): Promise<any[]> {
    const whereCondition = this.buildWhereCondition(schemas.settings, query);

    const results = await this.db
      .select()
      .from(schemas.settings)
      .where(whereCondition);

    return results.map(item => ({
      _id: item.key,
      key: item.key,
      value: this.parseSettingValue(item.value, item.type)
    }));
  }

  private async insertSetting(item: any): Promise<any> {
    const { value, type } = this.serializeSettingValue(item.value);

    const setting = {
      key: item.key || item._id,
      value,
      type,
      updatedAt: new Date()
    };

    const result = await this.db
      .insert(schemas.settings)
      .values(setting)
      .returning();

    return {
      _id: result[0].key,
      key: result[0].key,
      value: this.parseSettingValue(result[0].value, result[0].type)
    };
  }

  private async removeSettings(query: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.settings, query);

    if (!whereCondition) {
      throw new Error('删除操作必须提供有效的查询条件');
    }

    const result = await this.db
      .delete(schemas.settings)
      .where(whereCondition);

    return (result as any).changes || 0;
  }



  // 设置值序列化和反序列化
  private serializeSettingValue(value: any): { value: string; type: string } {
    if (typeof value === 'string') {
      return { value, type: 'string' };
    } else if (typeof value === 'number') {
      return { value: value.toString(), type: 'number' };
    } else if (typeof value === 'boolean') {
      return { value: value.toString(), type: 'boolean' };
    } else {
      return { value: JSON.stringify(value), type: 'object' };
    }
  }

  private parseSettingValue(value: string, type: string): any {
    switch (type) {
      case 'string':
        return value;
      case 'number':
        return parseFloat(value);
      case 'boolean':
        return value === 'true';
      case 'object':
        return JSON.parse(value);
      default:
        return value;
    }
  }

  // 兼容旧API的updateSettings方法
  private async updateSettings(query: any, value: any, multi: boolean): Promise<number> {
    const whereCondition = this.buildWhereCondition(schemas.settings, query);

    if (!whereCondition) {
      throw new Error('更新操作必须提供查询条件');
    }

    const { value: serializedValue, type } = this.serializeSettingValue(value.value);

    const updateData = {
      value: serializedValue,
      type,
      updatedAt: new Date()
    };

    const result = await this.db
      .update(schemas.settings)
      .set(updateData)
      .where(whereCondition);

    return (result as any).changes || 0;
  }

  // ==================== 配置管理方法 ====================

  /**
   * 初始化设置
   */
  private async initializeSettings(): Promise<void> {
    console.log('[LibSQL Storage] 初始化配置管理...');

    try {
      // 从数据库加载所有设置
      const settingsFromDb = await this.getAllConfigsFromDb();

      // 如果数据库为空，使用默认设置并保存到数据库
      if (Object.keys(settingsFromDb).length === 0) {
        console.log('[LibSQL Storage] 数据库为空，初始化默认设置...');
        await this.initializeDefaultSettings();
        return;
      }

      // 合并默认设置和数据库设置
      const mergedSettings = { ...DEFAULT_SETTINGS, ...settingsFromDb };

      // 更新缓存
      this.cache.setSettingsCache(mergedSettings);

      console.log('[LibSQL Storage] 配置管理初始化完成');
    } catch (error) {
      console.error('[LibSQL Storage] 初始化配置管理失败:', error);
      // 如果加载失败，使用默认设置
      this.cache.setSettingsCache(DEFAULT_SETTINGS);
    }
  }

  /**
   * 从数据库获取所有配置
   */
  private async getAllConfigsFromDb(): Promise<Partial<ISettings>> {
    try {
      const results = await this.db
        .select()
        .from(schemas.settings);

      const settings: any = {};

      for (const row of results) {
        try {
          // 根据类型解析值
          let value: any;
          switch (row.type) {
            case 'string':
              value = row.value;
              break;
            case 'number':
              value = parseFloat(row.value);
              break;
            case 'boolean':
              value = row.value === 'true';
              break;
            case 'object':
            case 'array':
              value = JSON.parse(row.value);
              break;
            default:
              value = row.value;
          }

          settings[row.key] = value;
        } catch (parseError) {
          console.warn('[LibSQL Storage] 解析配置项失败:', row.key, parseError);
        }
      }

      return settings;
    } catch (error) {
      console.error('[LibSQL Storage] 从数据库获取配置失败:', error);
      return {};
    }
  }

  /**
   * 初始化默认设置
   */
  private async initializeDefaultSettings(): Promise<void> {
    console.log('[LibSQL Storage] 初始化默认设置到数据库...');

    try {
      // 将默认设置保存到数据库
      for (const [key, value] of Object.entries(DEFAULT_SETTINGS)) {
        await this.setConfigInDb(key, value);
      }

      // 更新缓存
      this.cache.setSettingsCache(DEFAULT_SETTINGS);

      console.log('[LibSQL Storage] 默认设置初始化完成');
    } catch (error) {
      console.error('[LibSQL Storage] 初始化默认设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取完整设置
   */
  public async getSettings(): Promise<ISettings> {
    // 如果缓存可用，从缓存获取
    if (this.cache.isCacheEnabled()) {
      const cached = this.cache.getSettingsCache();
      if (cached) {
        return cached;
      }
    }

    // 否则从数据库加载
    const settings = await this.getAllConfigsFromDb();
    const mergedSettings = { ...DEFAULT_SETTINGS, ...settings };

    // 更新缓存
    if (this.cache.isCacheEnabled()) {
      this.cache.setSettingsCache(mergedSettings);
    }

    return mergedSettings;
  }

  /**
   * 更新设置（新API）
   */
  public async updateSettingsNew(newSettings: Partial<ISettings>): Promise<void> {
    console.log('[LibSQL Storage] 更新设置...');

    try {
      // 获取当前设置
      const currentSettings = await this.getSettings();

      // 合并设置
      const updatedSettings = { ...currentSettings, ...newSettings };

      // 保存到数据库
      for (const [key, value] of Object.entries(newSettings)) {
        await this.setConfigInDb(key, value);
      }

      // 更新缓存
      if (this.cache.isCacheEnabled() && this.isInitialized) {
        this.cache.setSettingsCache(updatedSettings);
      }

      console.log('[LibSQL Storage] 设置更新完成');
    } catch (error) {
      console.error('[LibSQL Storage] 更新设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个配置项
   */
  public async getConfig(key: string): Promise<any> {
    const settings = await this.getSettings();
    return settings[key as keyof ISettings];
  }

  /**
   * 设置单个配置项
   */
  public async setConfig(key: string, value: any): Promise<void> {
    await this.setConfigInDb(key, value);

    // 更新缓存中的单个配置项
    if (this.cache.isCacheEnabled() && this.isInitialized) {
      const currentSettings = this.cache.getSettingsCache() || await this.getSettings();
      const updatedSettings = { ...currentSettings, [key]: value };
      this.cache.setSettingsCache(updatedSettings);
    }
  }

  /**
   * 在数据库中设置配置项
   */
  private async setConfigInDb(key: string, value: any): Promise<void> {
    try {
      // 确定值的类型
      let type: string;
      let stringValue: string;

      if (typeof value === 'string') {
        type = 'string';
        stringValue = value;
      } else if (typeof value === 'number') {
        type = 'number';
        stringValue = value.toString();
      } else if (typeof value === 'boolean') {
        type = 'boolean';
        stringValue = value.toString();
      } else if (Array.isArray(value)) {
        type = 'array';
        stringValue = JSON.stringify(value);
      } else if (typeof value === 'object' && value !== null) {
        type = 'object';
        stringValue = JSON.stringify(value);
      } else {
        type = 'string';
        stringValue = String(value);
      }

      // 使用upsert操作
      await this.db
        .insert(schemas.settings)
        .values({
          key,
          value: stringValue,
          type,
          updatedAt: new Date()
        })
        .onConflictDoUpdate({
          target: schemas.settings.key,
          set: {
            value: stringValue,
            type,
            updatedAt: new Date()
          }
        });

    } catch (error) {
      console.error('[LibSQL Storage] 设置配置项到数据库失败:', key, error);
      throw error;
    }
  }

  /**
   * 重置所有设置为默认值
   */
  public async resetSettings(): Promise<void> {
    console.log('[LibSQL Storage] 重置所有设置为默认值...');

    try {
      // 清空数据库中的设置
      await this.db.delete(schemas.settings);

      // 重新初始化默认设置
      await this.initializeDefaultSettings();

      console.log('[LibSQL Storage] 设置已重置为默认值');
    } catch (error) {
      console.error('[LibSQL Storage] 重置设置失败:', error);
      throw error;
    }
  }

  /**
   * 广播设置更新到所有窗口和对话框
   */
  private async broadcastSettingsUpdate(): Promise<void> {
    try {
      // 使用事件发射器来避免循环依赖
      const { ipcMain } = require('electron');
      const settings = await this.getSettings();

      // 发送内部事件，让Application处理广播
      ipcMain.emit('settings-updated-internal', settings);

      console.log('[LibSQL Storage] 设置更新已广播');
    } catch (error) {
      console.error('[LibSQL Storage] 广播设置更新失败:', error);
    }
  }



  async close(): Promise<void> {
    await this.adapter.close();
  }
}
