import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { StyledSmallDialog, Title } from './style';
import { Button } from '@browser/core/components/Button';
import { ITheme } from '@mario-ai/shared';

type ClickEvent = (e: React.MouseEvent<HTMLDivElement>) => void;

export const SelectionDialog = observer(
  ({
    amount,
    visible,
    onDeleteClick,
    onCancelClick,
    theme,
  }: {
    amount: number;
    visible: boolean;
    onDeleteClick: ClickEvent;
    onCancelClick: ClickEvent;
    theme?: ITheme;
  }) => {
    return (
      <StyledSmallDialog visible={visible}>
        <Title>{amount} 已选中</Title>
        <Button style={{ marginLeft: 16 }} onClick={onDeleteClick}>
          删除选中
        </Button>
        <Button
          background={
            theme['dialog.lightForeground']
              ? 'rgba(255, 255, 255, 0.08)'
              : 'rgba(0, 0, 0, 0.08)'
          }
          foreground={theme['dialog.lightForeground'] ? 'white' : 'black'}
          style={{ marginLeft: 8 }}
          onClick={onCancelClick}
        >
          取消
        </Button>
      </StyledSmallDialog>
    );
  },
);
