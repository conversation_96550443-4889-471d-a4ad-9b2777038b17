// Web环境的WebUI工具函数
const WEBUI_PROTOCOL = 'wexond';
const WEBUI_BASE_URL = 'http://localhost:4444/'; // Web环境使用开发服务器地址
const WEBUI_URL_SUFFIX = '.html';

let storeNewTab = "";
let storeNewTabTemp = "";

export const updateStoreNewTab = (url: string) => {
  storeNewTab = url;
  if (!url) {
    storeNewTabTemp = url;
  }
}

export const updateStoreNewTabTemp = (url: string) => {
  storeNewTabTemp = url;
}

export const getWebUIURL = (hostname: string) => {
  if (hostname == "boke") {
    return "https://haikuoshijie.cn/";
  }
  if ("newtab" == hostname) {
    if (storeNewTabTemp) {
      return storeNewTabTemp;
    }
    if (storeNewTab) {
      return storeNewTab.split("\n")[0];
    }
    // 如果没有设置，返回默认的newtab页面
    return `${WEBUI_BASE_URL}newtab${WEBUI_URL_SUFFIX}`;
  }
  return `${WEBUI_BASE_URL}${hostname}${WEBUI_URL_SUFFIX}`;
}
