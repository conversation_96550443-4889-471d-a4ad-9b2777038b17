import * as React from 'react';

import { Dropdown } from '@browser/core/components/Dropdown';
import { Switch } from '@browser/core/components/Switch';
import { Title, Control, Header, Back, Row } from '../shared-styles';
import store from '../../store';
import { onSwitchChange } from '../../utils';
import { observer } from 'mobx-react-lite';
import { cn } from '@browser/utils/tailwind-helpers';
import { ICON_MORE, transparency } from '@mario-ai/shared';
import { NormalButton } from '../App';
import { ISearchEngine } from '@mario-ai/shared';

const SuggestionsToggle = observer(() => {
  const { suggestions } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('suggestions')}>
      <Title>展示搜索和网站建议</Title>
      <Control>
        <Switch value={suggestions} />
      </Control>
    </Row>
  );
});

const onSearchEngineChange = (value: string) => {
  const { searchEngines } = store.settings;
  store.settings.searchEngine = searchEngines.indexOf(
    searchEngines.find((x) => x.name === value),
  );
  store.save();
};

const SearchEngineRow = observer(() => {
  const se = store.searchEngine;

  return (
    <Row>
      <Title>搜索引擎</Title>
      <Control>
        <Dropdown defaultValue={se.name} onChange={onSearchEngineChange}>
          {Object.values(store.settings.searchEngines).map((item, key) => (
            <Dropdown.Item key={key} value={item.name}>
              {item.name}
            </Dropdown.Item>
          ))}
        </Dropdown>
      </Control>
    </Row>
  );
});

const onBackClick = () => {
  store.selectedSection = 'address-bar';
};

const onMoreClick = (data: ISearchEngine) => (
  e: React.MouseEvent<HTMLDivElement>,
) => {
  const { top, left } = e.currentTarget.getBoundingClientRect();
  store.menuInfo.left = left - store.menuRef.current.offsetWidth;
  store.menuInfo.top = top;

  store.editedSearchEngine = data;
  store.menuVisible = true;
};

export const SearchEngine = observer(({ data }: { data: ISearchEngine }) => {
  const isDefault = store.searchEngine.keyword === data.keyword;

  // TableRow 样式 - Tailwind 版本
  const tableRowClasses = cn(
    'flex items-center h-12 px-4 border-b border-black/8',
    // 粗体根据是否默认
    isDefault ? 'font-medium' : 'font-normal'
  );

  // TableCell 样式 - Tailwind 版本
  const tableCellClasses = cn(
    'flex-1 text-sm overflow-hidden text-ellipsis whitespace-nowrap'
  );

  // MoreButton 样式 - Tailwind 版本
  const moreButtonClasses = cn(
    'w-6 h-6 bg-center bg-no-repeat bg-contain cursor-pointer',
    'hover:opacity-80 transition-opacity duration-200',
    `opacity-[${transparency.icons.inactive}]`,
    // 过滤器根据主题
    store.theme['pages.lightForeground'] ? 'invert' : ''
  );

  const moreButtonStyle = {
    backgroundImage: `url(${ICON_MORE})`,
    backgroundSize: '16px'
  };

  return (
    <div className={tableRowClasses}>
      <div className={tableCellClasses}>
        <div>
          {data.name} {isDefault && '(Default)'}
        </div>
      </div>
      <div className={tableCellClasses}>
        <div>{data.keyword}</div>
      </div>
      <div className={tableCellClasses}>
        <div>{data.url}</div>
      </div>
      <div
        className={moreButtonClasses}
        style={moreButtonStyle}
        onClick={onMoreClick(data)}
      ></div>
    </div>
  );
});

const onAddClick = () => {
  store.dialogVisible = true;
  store.dialogContent = 'add-search-engine';
  store.searchEngineInputRef.current.value = '';
  store.searchEngineKeywordInputRef.current.value = '';
  store.searchEngineUrlInputRef.current.value = '';
};

export const ManageSearchEngines = observer(() => {
  return (
    <>
      <Header>
        <Back onClick={onBackClick} />
        管理搜索引擎
      </Header>
      <Row>
        <Title>地址栏搜索引擎</Title>
        <Control>
          <NormalButton onClick={onAddClick}>添加</NormalButton>
        </Control>
      </Row>
      <div className="w-full border border-black/8 rounded mt-4">
        <div className="flex items-center h-12 px-4 bg-black/2 border-b border-black/8">
          <div className="flex-1 text-sm font-medium">搜索引擎</div>
          <div className="flex-1 text-sm font-medium">关键词</div>
          <div className="flex-1 text-sm font-medium">地址</div>
          <div className="w-6"></div>
        </div>
        {store.settings.searchEngines.map((item, key) => (
          <SearchEngine key={item.keyword} data={item} />
        ))}
      </div>
    </>
  );
});

const onManageSearchEngines = () => {
  store.selectedSection = 'search-engines';
};

export const AddressBar = observer(() => {
  return (
    <>
      <Header>地址栏</Header>
      <SuggestionsToggle />
      <SearchEngineRow />
      <Row onClick={onManageSearchEngines}>
        <Title>管理搜索引擎</Title>
        <Control></Control>
      </Row>
    </>
  );
});
