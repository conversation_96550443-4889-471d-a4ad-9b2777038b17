import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import { Button } from '@browser/core/components/Button';
import { Switch } from '@browser/core/components/Switch';
import store from '../../../store';
import { eventUtils } from '@browser/core/utils/platform-lite';

const clearData = () => {
  // 发送清除浏览数据事件到主进程
  eventUtils.send('clear-browsing-data');

  // 关闭对话框
  store.dialogContent = null;

  // 显示成功消息
  console.log('[BrowsingDataDialog] Browsing data cleared successfully');
}

export default observer(() => {
  // Dialog 样式 - Tailwind 版本
  const dialogClasses = cn(
    'fixed w-[344px] p-4 left-1/2 top-1/2 rounded-[10px] z-[999]',
    'shadow-[0_8px_16px_rgba(0,0,0,0.24)] transition-opacity duration-200',
    'transform -translate-x-1/2 -translate-y-1/2 ml-auto',
    'bg-mario-dialog',
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black',
    store.dialogContent === 'privacy' ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
  );

  // Content 样式 - Tailwind 版本
  const contentClasses = cn(
    'mb-4'
  );

  // Option 样式 - Tailwind 版本
  const optionClasses = cn(
    'flex items-center justify-between py-2'
  );

  const labelClasses = cn(
    'text-sm'
  );

  if (store.dialogContent !== 'privacy') return null;

  return (
    <div className={dialogClasses}>
      <div className="text-base mb-4">清除浏览数据</div>
      <div className={contentClasses}>
        <div className={optionClasses}>
          <div className={labelClasses}>浏览历史记录</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>Cookie 和其他网站数据</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>缓存的图片和文件</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>下载历史记录</div>
          <Switch dense value={true} />
        </div>
        <div className={optionClasses}>
          <div className={labelClasses}>自动填充表单数据</div>
          <Switch dense value={true} />
        </div>
      </div>
      <div className="float-right flex mt-6">
        <Button background="transparent" foreground="#3F51B5" onClick={() => store.dialogContent = null}>
          取消
        </Button>
        <Button background="transparent" foreground="#3F51B5" onClick={clearData}>
          立即清除
        </Button>
      </div>
      <div style={{ clear: 'both' }}></div>
    </div>
  );
});
