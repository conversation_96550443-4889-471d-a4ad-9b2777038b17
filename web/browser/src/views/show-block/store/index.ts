import { eventUtils } from '@browser/core/utils/platform-lite';
import {makeObservable, observable} from 'mobx';
import * as React from 'react';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public titleRef = React.createRef<HTMLInputElement>();

  public list: any[] = [];
  public adListUrls: String = "";
  public filterCount = 0;

  public constructor() {
    super();
    makeObservable(this, {
      list: observable,
      adListUrls: observable,
      filterCount: observable
    });
    (async () => {
      this.adListUrls = await eventUtils.invoke('get-ad-lists');
      this.filterCount = await eventUtils.invoke('get-ad-filter-count');
      console.log("get-ad-lists2");
    })();
    eventUtils.on('data', async (e, data) => {
      this.list = data.data;
      this.filterCount = data.count;
    });
  }

}

export default new Store();
