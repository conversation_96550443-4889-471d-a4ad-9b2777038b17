import { eventUtils } from '@browser/core/utils/platform-lite';
import {makeObservable, observable} from 'mobx';
import * as React from 'react';
import { LiteDialogStore } from '@browser/core/utils/platform-lite';

export class Store extends LiteDialogStore {
  public titleRef = React.createRef<HTMLInputElement>();

  public list: any[] = [];
  public adListUrls: String = "";
  public filterCount = 0;

  public constructor() {
    // 广告拦截对话框启用失焦隐藏，但使用延迟避免误触发
    super({ hideOnBlur: true });
    makeObservable(this, {
      list: observable,
      adListUrls: observable,
      filterCount: observable
    });
    (async () => {
      this.adListUrls = await eventUtils.invoke('get-ad-lists');
      this.filterCount = await eventUtils.invoke('get-ad-filter-count');
      console.log("get-ad-lists2");
    })();
    eventUtils.on('data', async (e, data) => {
      this.list = data.data;
      this.filterCount = data.count;
    });

    // 添加广告拦截对话框特有的失焦隐藏功能
    // 使用更长的延迟，因为用户需要时间查看内容
    if (this.hideOnBlur) {
      let blurTimer: NodeJS.Timeout | null = null;

      const handleBlur = () => {
        blurTimer = setTimeout(() => {
          console.log('[ShowBlockStore] Window blur detected, hiding dialog');
          this.hide();
        }, 500); // 500ms 延迟，比菜单更长，给用户更多时间
      };

      const handleFocus = () => {
        if (blurTimer) {
          clearTimeout(blurTimer);
          blurTimer = null;
        }
      };

      window.addEventListener('blur', handleBlur);
      window.addEventListener('focus', handleFocus);

      // 清理函数
      const cleanup = () => {
        window.removeEventListener('blur', handleBlur);
        window.removeEventListener('focus', handleFocus);
        if (blurTimer) {
          clearTimeout(blurTimer);
        }
      };

      // 当对话框隐藏时清理事件监听器
      const originalHide = this.hide.bind(this);
      this.hide = () => {
        cleanup();
        originalHide();
      };
    }
  }

}

export default new Store();
