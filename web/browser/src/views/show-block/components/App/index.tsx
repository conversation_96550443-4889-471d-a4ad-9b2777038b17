import * as React from 'react';
import {observer} from 'mobx-react-lite';

import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';
import {Button} from '@browser/core/components/Button';
import {UIStyle} from '@browser/core/styles/default-styles';

import MyInput from "./input";
import { eventUtils } from '@browser/core/utils/platform-lite';

const onDone = () => {
  console.log('[ShowBlock] Hiding dialog');
  store.hide();
  // 发送隐藏事件到主进程
  const webContentsId = (window as any).webContents?.id || 'unknown';
  eventUtils.send(`hide-${webContentsId}`);
};
const onChange = () => {
  store.adListUrls = store.titleRef.current.value;
};
const save = () => {
  console.log(store.adListUrls);
  eventUtils.send('update-ad-lists', store.adListUrls);
  store.hide();
};
export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  const appClasses = cn(
    'p-4 rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]',
    // textfield 和 dropdown 样式
    '[&_.textfield]:w-[255px] [&_.textfield]:ml-auto',
    '[&_.dropdown]:w-[255px] [&_.dropdown]:ml-auto',
    // 文本颜色根据主题
    store.theme['dialog.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'text-base mb-4 font-roboto font-normal' // font-size: 16px, margin-bottom: 16px
  );

  // Row 样式 - Tailwind 版本
  const rowClasses = cn(
    'w-full h-12 items-center flex' // height: 48px
  );

  // Label 样式 - Tailwind 版本
  const labelClasses = cn(
    'text-xs' // font-size: 12px
  );

  // SmallRow 样式 - Tailwind 版本
  const smallRowClasses = cn(
    'w-full h-8 items-center flex' // height: 32px
  );

  // SmallRow2 样式 - Tailwind 版本 (稍小的行)
  const smallRow2Classes = cn(
    'w-full h-6 items-center flex' // height: 24px
  );

  // ellipsis-line 样式 - Tailwind 版本
  const ellipsisLineClasses = cn(
    'border border-gray-400 p-1 w-[400px] overflow-hidden text-ellipsis whitespace-nowrap'
  );

  // Buttons 样式 - Tailwind 版本
  const buttonsClasses = cn(
    'w-full flex items-center justify-end',
    // 子元素 button 的右边距 (除了最后一个)
    '[&_.button:not(:last-child)]:mr-2'
  );

  return (
    <div className={appClasses}>
      <UIStyle/>
      <div className={titleClasses}>{"当前页面拦截（" + store.list.length + "）"}</div>
      <div style={{overflow: "auto", maxHeight: 200}}>
        {store.list.map((item, index) => (
          <div key={index}>
            <div className={smallRowClasses}>
              <div className={cn(labelClasses, ellipsisLineClasses)}>{item.url}</div>
            </div>
            <div className={smallRow2Classes}>
              <div className={labelClasses}>{"被 " + item.filter + " 拦截"}</div>
            </div>
          </div>
        ))}
      </div>
      <div className={rowClasses}>
        <div className={labelClasses}>{"编辑订阅地址，当前规则数：" + store.filterCount}</div>
      </div>
      <div className={cn(rowClasses, 'min-h-[150px]')}>
        <MyInput
          type={"textarea"}
          tabIndex={0}
          placeholder={"订阅地址，一行一个，支持ublock/adblockplus/adguard格式"}
          ref={store.titleRef}
          onChange={onChange}
          value={store.adListUrls}
        />
      </div>
      <div className={buttonsClasses}>
        <Button onClick={save}>保存</Button>
        <Button onClick={onDone}>取消</Button>
      </div>
    </div>
  );
});
