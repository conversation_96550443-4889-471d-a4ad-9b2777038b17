import { eventUtils } from '@browser/core/utils/platform-lite';
import { makeObservable, observable } from 'mobx';
import { LiteDialogStore, windowControls } from '@browser/core/utils/platform-lite';
export class Store extends LiteDialogStore {
  public alwaysOnTop = false;

  public updateAvailable = false;

  public constructor() {
    super();

    makeObservable(this, {
      alwaysOnTop: observable,
      updateAvailable: observable,
    });

    this.init();

    eventUtils.on('update-available', () => {
      this.updateAvailable = true;
    });

    // 添加菜单特有的失焦隐藏功能
    if (this.hideOnBlur) {
      window.addEventListener('blur', () => {
        console.log('[MenuStore] Window blur detected, hiding menu');
        this.hide();
      });
    }
  }

  public async init() {
    if (windowControls.getCurrentWindow()) {
      this.alwaysOnTop = windowControls.getCurrentWindow().isAlwaysOnTop();
    }

    this.updateAvailable = await eventUtils.invoke('is-update-available');
  }

  public async save() {
    eventUtils.send('save-settings', {
      settings: JSON.stringify(this.settings),
    });
  }
}

export default new Store();
