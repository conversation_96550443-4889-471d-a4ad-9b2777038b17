import { eventUtils } from '@browser/core/utils/platform-lite';
import { makeObservable, observable } from 'mobx';
import { LiteDialogStore, windowControls } from '@browser/core/utils/platform-lite';
export class Store extends LiteDialogStore {
  public alwaysOnTop = false;

  public updateAvailable = false;

  public constructor() {
    super();

    makeObservable(this, {
      alwaysOnTop: observable,
      updateAvailable: observable,
    });

    this.init();

    eventUtils.on('update-available', () => {
      this.updateAvailable = true;
    });

    // 添加菜单特有的失焦隐藏功能
    if (this.hideOnBlur) {
      // 使用延迟来避免误触发
      let blurTimer: NodeJS.Timeout;

      const handleBlur = () => {
        blurTimer = setTimeout(() => {
          console.log('[MenuStore] Window blur detected, hiding menu');
          this.hide();
        }, 100); // 100ms 延迟，避免快速焦点切换时误触发
      };

      const handleFocus = () => {
        if (blurTimer) {
          clearTimeout(blurTimer);
          blurTimer = null;
        }
      };

      window.addEventListener('blur', handleBlur);
      window.addEventListener('focus', handleFocus);

      // 清理函数
      const cleanup = () => {
        window.removeEventListener('blur', handleBlur);
        window.removeEventListener('focus', handleFocus);
        if (blurTimer) {
          clearTimeout(blurTimer);
        }
      };

      // 当菜单隐藏时清理事件监听器
      const originalHide = this.hide.bind(this);
      this.hide = () => {
        cleanup();
        originalHide();
      };
    }
  }

  public async init() {
    try {
      // 通过 IPC 获取窗口状态，而不是直接调用不存在的方法
      this.alwaysOnTop = await eventUtils.invoke('is-always-on-top');
    } catch (error) {
      console.warn('[MenuStore] Failed to get always-on-top status:', error);
      this.alwaysOnTop = false;
    }

    try {
      this.updateAvailable = await eventUtils.invoke('is-update-available');
    } catch (error) {
      console.warn('[MenuStore] Failed to get update status:', error);
      this.updateAvailable = false;
    }
  }

  public async save() {
    eventUtils.send('save-settings', {
      settings: JSON.stringify(this.settings),
    });
  }
}

export default new Store();
