import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { QuickMenu } from '../QuickMenu';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

export const App = observer(() => {
  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  // 确保菜单有固定宽度，与主进程中的 menuWidth = 330 保持一致
  const appClasses = cn(
    'w-[330px] rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]'
  );

  return (
    <div className={appClasses}>
      <UIStyle />
      <QuickMenu />
    </div>
  );
});
