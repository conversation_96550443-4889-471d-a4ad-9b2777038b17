import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { QuickMenu } from '../QuickMenu';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';
import { cn } from '@browser/utils/tailwind-helpers';

export const App = observer(() => {
  const appRef = React.useRef<HTMLDivElement>(null);

  // 添加点击空白处关闭菜单的逻辑
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (appRef.current && !appRef.current.contains(event.target as Node)) {
        console.log('[Menu] Click outside detected, hiding menu');
        store.hide();
      }
    };

    // 延迟添加事件监听器，避免立即触发
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 100);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // StyledApp 样式 - Tailwind 版本 (继承 DialogStyle)
  // 确保菜单有固定宽度，与主进程中的 menuWidth = 330 保持一致
  const appClasses = cn(
    'w-[330px] rounded-[10px] shadow-dialog bg-mario-dialog',
    'animate-[fadeIn_0.15s_ease-out]'
  );

  return (
    <div ref={appRef} className={appClasses}>
      <UIStyle />
      <QuickMenu />
    </div>
  );
});
