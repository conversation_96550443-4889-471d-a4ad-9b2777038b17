import * as React from 'react';
import { observer } from 'mobx-react-lite';

import { IBookmark } from '@mario-ai/shared';
import store from '../../store';
import { ListItem } from '@browser/core/components/ListItem';
import { getBookmarkTitle } from '../../utils';
import { ICON_PAGE, ICON_FOLDER, ICON_MORE } from '@mario-ai/shared';
import { cn } from '@browser/utils/tailwind-helpers';

const onClick = (item: IBookmark) => (e: React.MouseEvent<HTMLDivElement>) => {
  const index = store.selectedItems.indexOf(item._id);

  if (e.ctrlKey) {
    if (index === -1) {
      store.selectedItems.push(item._id);
    } else {
      store.selectedItems.splice(index, 1);
    }
  } else {
    store.selectedItems = [item._id];
  }
};

const onDoubleClick = (item: IBookmark) => () => {
  if (item.isFolder) {
    store.currentFolder = item._id;
  } else {
    const win = window.open(item.url, '_blank');
    try {
      win.focus();
    } catch (e) {
      console.error(e);
    }
  }
};

const onMoreClick = (data: IBookmark) => (e: any) => {
  e.stopPropagation();

  const { left, top } = e.currentTarget.getBoundingClientRect();

  store.menuVisible = true;
  store.menuLeft = left - 130;
  store.menuTop = top;
  store.currentBookmark = data;
};

const onContextMenu = (data: IBookmark) => (e: any) => {
  e.preventDefault();

  onClick(data)(e);

  const { pageX, pageY } = e;

  store.menuVisible = true;
  store.menuLeft = pageX;
  store.menuTop = pageY;
  store.currentBookmark = data;
};

export const Bookmark = observer(({ data }: { data: IBookmark }) => {
  const selected = store.selectedItems.includes(data._id);

  let favicon = data.favicon;
  let customFavicon = false;

  if (data.isFolder) {
    favicon = ICON_FOLDER;
    customFavicon = true;
  } else {
    if (favicon) {
      if (favicon.startsWith('data:')) {
        favicon = data.favicon;
      } else {
        favicon = store.favicons.get(data.favicon);
      }
    } else {
      favicon = ICON_PAGE;
      customFavicon = true;
    }
  }

  // Favicon 样式 - Tailwind 版本
  const faviconClasses = cn(
    'h-4 w-4 mr-6 bg-center bg-no-repeat bg-contain'
  );

  // Title 样式 - Tailwind 版本
  const titleClasses = cn(
    'flex-1 whitespace-nowrap overflow-hidden text-ellipsis mr-4',
    store.theme['pages.lightForeground'] ? 'text-white' : 'text-black'
  );

  // Site 样式 - Tailwind 版本
  const siteClasses = cn(
    'flex-1 opacity-54 whitespace-nowrap overflow-hidden text-ellipsis'
  );

  // More 样式 - Tailwind 版本
  const moreClasses = cn(
    'h-6 w-6 cursor-pointer opacity-54 bg-center bg-no-repeat bg-contain',
    'hover:opacity-100'
  );

  return (
    <ListItem
      onContextMenu={onContextMenu(data)}
      onDoubleClick={onDoubleClick(data)}
      key={data._id}
      onClick={onClick(data)}
      selected={selected}
      style={{ borderRadius: 0 }}
    >
      <div
        className={faviconClasses}
        style={{
          backgroundImage: `url(${favicon})`,
          filter:
            store.theme['pages.lightForeground'] && customFavicon
              ? 'invert(100%)'
              : 'none',
        }}
      />
      <div className={titleClasses}>{getBookmarkTitle(data)}</div>
      <div className={siteClasses}>{data.url}</div>
      {!data.static && (
        <div
          className={moreClasses}
          onClick={onMoreClick(data)}
          style={{
            backgroundImage: `url(${ICON_MORE})`,
            filter: store.theme['pages.lightForeground'] ? 'invert(100%)' : 'none',
          }}
        />
      )}
    </ListItem>
  );
});
