import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { ERROR_COLOR, BLUE_500, transparency, EASING_FUNCTION } from '@mario-ai/shared';

export type TestFunction = (str: string) => boolean;

export interface TextFieldProps {
  color?: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  onIconClick?: (target: Textfield) => void;
  inputType?: 'text' | 'email' | 'password' | 'number';
  style?: any;
  test?: TestFunction;
  width?: number;
  onChange?: (value: string) => void;
  delay?: number;
  dark?: boolean;
}

interface State {
  activated: boolean;
  focused: boolean;
  error: boolean;
  value: string;
}

export class Textfield extends React.PureComponent<TextFieldProps, State> {
  public inputRef = React.createRef<HTMLInputElement>();
  private timer: number;

  private static defaultProps: TextFieldProps = {
    color: BLUE_500,
    inputType: 'text',
    width: 280,
    delay: 200,
  };

  public state: State = {
    activated: false,
    focused: false,
    error: false,
    value: undefined,
  };

  public get value() {
    return this.inputRef.current.value;
  }

  public set value(str: string) {
    this.inputRef.current.value = str;

    this.setState({
      activated: !!str.length,
      value: str,
    });
  }

  private onClick = () => {
    this.inputRef.current.focus();
  };

  public onFocus = () => {
    this.setState({
      activated: true,
      focused: true,
    });
  };

  public onBlur = () => {
    this.setState({
      activated: this.value.length !== 0,
      focused: false,
    });
  };

  public onIconClick = (e: React.SyntheticEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();

    const { onIconClick } = this.props;

    if (typeof onIconClick === 'function') {
      onIconClick(this);
    }
  };

  public test(fn?: TestFunction) {
    const { test } = this.props;
    if (fn == null && test == null) return true;

    const correct = fn != null ? fn(this.value) : test(this.value);

    this.setState({
      error: !correct,
      focused: !correct,
      activated: this.value.length !== 0 || !correct,
    });

    return correct;
  }

  public onInput = () => {
    clearTimeout(this.timer);
    const { onChange } = this.props;

    this.setState({ error: false, value: this.inputRef.current.value });
    if (onChange) {
      onChange(this.inputRef.current.value);
    }
  };

  public clear() {
    this.value = '';

    this.setState({
      activated: false,
      error: false,
      focused: false,
    });
  }

  public render() {
    const {
      color,
      label,
      placeholder,
      icon,
      inputType,
      style,
      width,
      dark,
    } = this.props;
    const { activated, focused, error, value } = this.state;

    const hasLabel = label != null && label !== '';
    const hasIcon = icon != null && icon !== '';

    const primaryColor = error ? ERROR_COLOR : color;

    // 容器样式
    const containerClasses = cn(
      'relative overflow-hidden cursor-text select-none',
      'rounded-t'
    );

    // 容器动态样式
    const containerStyle: React.CSSProperties = {
      width: width === undefined ? 280 : width,
      backgroundColor: dark ? 'rgba(255, 255, 255, 0.06)' : '#f5f5f5',
      ...style
    };

    // 输入框样式
    const inputClasses = cn(
      'w-full h-[55px] text-base border-none outline-none bg-transparent select-auto',
      'font-roboto font-normal'
    );

    // 输入框动态样式
    const inputStyle: React.CSSProperties = {
      paddingLeft: '12px',
      paddingTop: hasLabel ? '12px' : '0px',
      paddingRight: hasIcon ? '48px' : '12px',
      borderBottom: `1px solid ${dark ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.42)'}`,
      caretColor: primaryColor,
    };

    // 标签样式
    const labelClasses = cn(
      'absolute left-3 pointer-events-none transition-all duration-200',
      'top-1/2 -translate-y-1/2'
    );

    const labelStyle: React.CSSProperties = {
      fontSize: activated ? '12px' : '16px',
      marginTop: activated ? '-12px' : '0px',
      color: focused 
        ? primaryColor 
        : dark 
          ? `rgba(255, 255, 255, ${transparency.text.medium})` 
          : `rgba(0, 0, 0, ${transparency.text.medium})`,
      fontWeight: activated ? 500 : 400,
      transitionTimingFunction: EASING_FUNCTION,
    };

    // 指示器样式
    const indicatorClasses = cn(
      'h-0.5 absolute bottom-0 left-1/2 -translate-x-1/2 transition-all duration-200'
    );

    const indicatorStyle: React.CSSProperties = {
      width: focused ? '100%' : '0%',
      backgroundColor: primaryColor,
      transitionTimingFunction: EASING_FUNCTION,
    };

    // 图标样式
    const iconClasses = cn(
      'w-9 h-9 absolute right-2 top-1/2 -translate-y-1/2',
      'rounded-full overflow-hidden cursor-pointer transition-all duration-200',
      'flex items-center justify-center',
      'hover:bg-black hover:bg-opacity-12'
    );

    const iconStyle: React.CSSProperties = {
      opacity: transparency.icons.inactive,
      backgroundImage: `url(${icon})`,
      backgroundSize: '24px',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center',
      filter: dark ? 'invert(100%)' : 'none',
    };

    return (
      <div
        className={cn(containerClasses, 'textfield')}
        onClick={this.onClick}
        style={containerStyle}
      >
        <input
          className={inputClasses}
          ref={this.inputRef}
          type={inputType || Textfield.defaultProps.inputType}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
          placeholder={label == null || focused ? placeholder : null}
          onInput={this.onInput}
          spellCheck={false}
          value={value}
          style={inputStyle}
        />
        
        {hasLabel && (
          <div
            className={labelClasses}
            style={labelStyle}
          >
            {label}
          </div>
        )}
        
        {hasIcon && (
          <div
            className={iconClasses}
            onClick={this.onIconClick}
            style={iconStyle}
          />
        )}
        
        <div
          className={indicatorClasses}
          style={indicatorStyle}
        />
      </div>
    );
  }
}
